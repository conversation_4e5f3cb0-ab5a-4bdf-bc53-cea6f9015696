* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

.main-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    flex: 1;
}

.token-section, .info-section {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.token-section h2, .info-section h2 {
    color: #4a5568;
    margin-bottom: 20px;
    font-size: 1.5rem;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #2d3748;
}

.form-group input {
    width: 100%;
    padding: 12px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group small {
    display: block;
    margin-top: 5px;
    color: #718096;
    font-size: 0.875rem;
}

.button-group {
    display: flex;
    gap: 15px;
    margin-bottom: 30px;
}

.generate-btn, .clear-btn {
    flex: 1;
    padding: 15px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.generate-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.generate-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.clear-btn {
    background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
    color: white;
}

.clear-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(229, 62, 62, 0.4);
}

.generate-btn:disabled, .clear-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.token-display {
    margin-bottom: 30px;
}

.token-display h3 {
    color: #4a5568;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.status-display {
    padding: 15px;
    border-radius: 8px;
    background: #f7fafc;
    border-left: 4px solid #667eea;
    margin-bottom: 20px;
}

.token-info {
    margin-top: 20px;
}

.token-info label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #2d3748;
}

.token-input-group {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
}

.token-input-group input {
    flex: 1;
    padding: 12px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 0.9rem;
    background: #f7fafc;
}

.copy-btn {
    padding: 12px 20px;
    background: #38a169;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.2s ease;
}

.copy-btn:hover {
    background: #2f855a;
}

.navigation {
    text-align: center;
}

.nav-link {
    display: inline-block;
    padding: 12px 24px;
    background: #4299e1;
    color: white;
    text-decoration: none;
    border-radius: 8px;
    font-weight: 600;
    transition: background 0.2s ease;
}

.nav-link:hover {
    background: #3182ce;
}

.info-content {
    margin-bottom: 30px;
}

.step {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
}

.step-number {
    width: 30px;
    height: 30px;
    background: #667eea;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 15px;
    flex-shrink: 0;
}

.step-content h4 {
    color: #2d3748;
    margin-bottom: 5px;
}

.step-content p {
    color: #718096;
    line-height: 1.5;
}

.tips {
    background: #f0fff4;
    border: 1px solid #9ae6b4;
    border-radius: 8px;
    padding: 20px;
}

.tips h3 {
    color: #2f855a;
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.tips ul {
    list-style: none;
    padding: 0;
}

.tips li {
    color: #2d3748;
    margin-bottom: 8px;
    padding-left: 20px;
    position: relative;
}

.tips li:before {
    content: "•";
    color: #38a169;
    font-weight: bold;
    position: absolute;
    left: 0;
}

.footer {
    text-align: center;
    padding: 20px;
    color: white;
    opacity: 0.8;
    margin-top: 30px;
}

@media (max-width: 768px) {
    .main-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .button-group {
        flex-direction: column;
    }
    
    .token-input-group {
        flex-direction: column;
    }
    
    .header h1 {
        font-size: 2rem;
    }
}
