# 邮箱批量生成器

一个基于Web的邮箱批量生成工具，支持Token管理和邮箱批量创建。

## 功能特点

- 🔐 **独立Token管理** - 专门的Token生成页面
- 📧 **批量邮箱生成** - 支持批量创建邮箱账户
- 💾 **永久Token存储** - Token一次生成，永久使用
- 📊 **详细结果统计** - 成功/失败统计和详细日志
- 📁 **数据导出** - 支持导出和复制结果

## 使用流程

### 1. 生成Token

1. 打开 `token.html` 页面
2. 输入管理员邮箱和密码（已预填默认值）
3. 点击"生成Token"按钮
4. 复制生成的Token

### 2. 生成邮箱

1. 打开 `index.html` 页面
2. 粘贴Token到"访问Token"输入框
3. 配置邮箱生成参数：
   - 邮箱前缀
   - 起始编号
   - 生成数量
   - 权限身份名（可选）
4. 点击"开始生成"

## 页面说明

### Token生成页面 (token.html)
- **功能**：生成和管理访问Token
- **特点**：Token永久保存在浏览器本地存储
- **操作**：生成Token、清除Token、复制Token

### 邮箱生成页面 (index.html)
- **功能**：批量创建邮箱账户
- **特点**：实时进度显示、详细结果统计
- **操作**：配置参数、生成邮箱、导出结果

## 默认配置

- **管理员邮箱**：<EMAIL>
- **管理员密码**：123456
- **邮箱域名**：ai999.dpdns.org
- **默认密码**：Ai12345@
- **API地址**：https://eoceshi.552500.xyz

## 文件结构

```
├── index.html      # 邮箱生成主页面
├── token.html      # Token生成页面
├── script.js       # 邮箱生成逻辑
├── token.js        # Token管理逻辑
├── style.css       # 样式文件
└── README.md       # 说明文档
```

## 技术特点

- 纯前端实现，无需服务器
- 响应式设计，支持移动端
- 本地存储Token，安全便捷
- 模块化设计，功能分离
- 友好的用户界面和交互体验

## 注意事项

1. Token生成需要有效的管理员账户
2. Token保存在浏览器本地存储中
3. 清除浏览器数据会删除保存的Token
4. 建议定期备份重要的Token
5. 单次最多生成100个邮箱账户
