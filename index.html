<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邮箱批量生成器</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1><i class="fas fa-envelope"></i> 邮箱批量生成器</h1>
            <p>快速批量创建邮箱账户</p>
        </header>

        <div class="main-content">
            <div class="form-section">
                <h2>Token状态</h2>

                <div class="token-status">
                    <div id="tokenStatus" class="status-display">
                        <span id="tokenStatusText">检查Token状态中...</span>
                    </div>
                    <button id="refreshTokenBtn" class="refresh-btn">
                        <i class="fas fa-sync-alt"></i> 刷新Token状态
                    </button>
                </div>

                <div class="form-group">
                    <small>Token将自动从token.txt文件加载，如需重新生成请运行generate_token.py</small>
                </div>

                <h2>生成设置</h2>

                <div class="form-group">
                    <label for="prefix">邮箱前缀:</label>
                    <input type="text" id="prefix" placeholder="例如: user" value="user">
                    <small>将生成 <EMAIL>, <EMAIL> ...</small>
                </div>

                <div class="form-group">
                    <label for="startNumber">起始编号:</label>
                    <input type="number" id="startNumber" value="1" min="1">
                </div>

                <div class="form-group">
                    <label for="count">生成数量:</label>
                    <input type="number" id="count" value="10" min="1" max="100">
                    <small>单次最多生成100个邮箱</small>
                </div>

                <div class="form-group">
                    <label for="roleName">权限身份名 (可选):</label>
                    <input type="text" id="roleName" placeholder="留空使用默认权限">
                </div>

                <button id="generateBtn" class="generate-btn">
                    <i class="fas fa-magic"></i> 开始生成
                </button>
            </div>

            <div class="result-section">
                <h2>生成结果</h2>
                <div class="status-bar">
                    <span id="statusText">等待生成...</span>
                    <div class="progress-bar">
                        <div id="progressFill" class="progress-fill"></div>
                    </div>
                </div>

                <div class="result-stats">
                    <div class="stat-item">
                        <span class="stat-label">成功:</span>
                        <span id="successCount" class="stat-value success">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">失败:</span>
                        <span id="failCount" class="stat-value error">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">总计:</span>
                        <span id="totalCount" class="stat-value">0</span>
                    </div>
                </div>

                <div class="result-content">
                    <div class="result-tabs">
                        <button class="tab-btn active" data-tab="success">成功列表</button>
                        <button class="tab-btn" data-tab="failed">失败列表</button>
                        <button class="tab-btn" data-tab="all">完整日志</button>
                    </div>

                    <div class="tab-content">
                        <div id="successTab" class="tab-panel active">
                            <div class="export-actions">
                                <button id="exportSuccess" class="export-btn">
                                    <i class="fas fa-download"></i> 导出成功列表
                                </button>
                                <button id="copySuccess" class="copy-btn">
                                    <i class="fas fa-copy"></i> 复制
                                </button>
                            </div>
                            <div id="successList" class="result-list"></div>
                        </div>

                        <div id="failedTab" class="tab-panel">
                            <div class="export-actions">
                                <button id="exportFailed" class="export-btn">
                                    <i class="fas fa-download"></i> 导出失败列表
                                </button>
                                <button id="copyFailed" class="copy-btn">
                                    <i class="fas fa-copy"></i> 复制
                                </button>
                            </div>
                            <div id="failedList" class="result-list"></div>
                        </div>

                        <div id="allTab" class="tab-panel">
                            <div class="export-actions">
                                <button id="exportAll" class="export-btn">
                                    <i class="fas fa-download"></i> 导出完整日志
                                </button>
                                <button id="copyAll" class="copy-btn">
                                    <i class="fas fa-copy"></i> 复制
                                </button>
                            </div>
                            <div id="allList" class="result-list"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer class="footer">
        <p>&copy; 2024 邮箱批量生成器 - 基于 Cloudflare Pages 部署</p>
    </footer>

    <script src="script.js"></script>
</body>
</html>
