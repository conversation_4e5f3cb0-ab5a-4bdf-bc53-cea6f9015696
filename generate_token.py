#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Token生成器
自动生成访问令牌并保存到token.txt文件中
"""

import requests
import json
import os
from datetime import datetime

# API配置
API_CONFIG = {
    'base_url': 'https://eoceshi.552500.xyz',
    'admin_email': '<EMAIL>',
    'admin_password': '123456'
}

# 文件路径
TOKEN_FILE = 'token.txt'
LOG_FILE = 'token_log.txt'

def log_message(message):
    """记录日志信息"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    log_entry = f"[{timestamp}] {message}"
    print(log_entry)
    
    # 写入日志文件
    try:
        with open(LOG_FILE, 'a', encoding='utf-8') as f:
            f.write(log_entry + '\n')
    except Exception as e:
        print(f"写入日志失败: {e}")

def generate_token():
    """生成访问令牌"""
    log_message("开始生成Token...")
    
    try:
        # 准备请求数据
        request_data = {
            'email': API_CONFIG['admin_email'],
            'password': API_CONFIG['admin_password']
        }
        
        log_message(f"使用管理员邮箱: {API_CONFIG['admin_email']}")
        
        # 发送请求
        response = requests.post(
            f"{API_CONFIG['base_url']}/api/public/genToken",
            headers={'Content-Type': 'application/json'},
            json=request_data,
            timeout=30
        )
        
        log_message(f"API响应状态码: {response.status_code}")
        
        # 解析响应
        if response.status_code == 200:
            data = response.json()
            log_message(f"API响应数据: {json.dumps(data, ensure_ascii=False)}")
            
            if data.get('code') == 200 and 'data' in data and 'token' in data['data']:
                token = data['data']['token']
                log_message(f"Token生成成功: {token[:8]}...")
                return token
            else:
                error_msg = data.get('message', '未知错误')
                log_message(f"Token生成失败: {error_msg}")
                return None
        else:
            log_message(f"HTTP请求失败: {response.status_code} - {response.text}")
            return None
            
    except requests.exceptions.Timeout:
        log_message("请求超时，请检查网络连接")
        return None
    except requests.exceptions.ConnectionError:
        log_message("连接失败，请检查网络连接和API地址")
        return None
    except requests.exceptions.RequestException as e:
        log_message(f"请求异常: {e}")
        return None
    except json.JSONDecodeError as e:
        log_message(f"JSON解析失败: {e}")
        return None
    except Exception as e:
        log_message(f"未知错误: {e}")
        return None

def save_token(token):
    """保存Token到文件"""
    try:
        with open(TOKEN_FILE, 'w', encoding='utf-8') as f:
            f.write(token)
        log_message(f"Token已保存到 {TOKEN_FILE}")
        return True
    except Exception as e:
        log_message(f"保存Token失败: {e}")
        return False

def read_existing_token():
    """读取现有Token"""
    try:
        if os.path.exists(TOKEN_FILE):
            with open(TOKEN_FILE, 'r', encoding='utf-8') as f:
                token = f.read().strip()
            if token:
                log_message(f"找到现有Token: {token[:8]}...")
                return token
        log_message("未找到现有Token文件")
        return None
    except Exception as e:
        log_message(f"读取Token文件失败: {e}")
        return None

def main():
    """主函数"""
    print("=" * 50)
    print("Token生成器")
    print("=" * 50)
    
    # 检查现有Token
    existing_token = read_existing_token()
    if existing_token:
        choice = input("发现现有Token，是否重新生成？(y/N): ").strip().lower()
        if choice not in ['y', 'yes']:
            log_message("使用现有Token，程序退出")
            return
    
    # 生成新Token
    token = generate_token()
    if token:
        if save_token(token):
            log_message("Token生成和保存完成！")
            print(f"\n✅ 成功生成Token: {token[:8]}...")
            print(f"📁 Token已保存到: {TOKEN_FILE}")
            print("🚀 现在可以使用邮箱生成器了！")
        else:
            log_message("Token保存失败")
            print("❌ Token保存失败")
    else:
        log_message("Token生成失败")
        print("❌ Token生成失败，请检查网络连接和配置")

if __name__ == "__main__":
    main()
