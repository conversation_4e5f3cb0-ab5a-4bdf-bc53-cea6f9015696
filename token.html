<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Token生成器</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1><i class="fas fa-key"></i> Token生成器</h1>
            <p>生成永久访问令牌</p>
        </header>

        <div class="token-content">
            <div class="token-form-section">
                <h2>管理员配置</h2>

                <div class="form-group">
                    <label for="adminEmail">管理员邮箱:</label>
                    <input type="email" id="adminEmail" placeholder="<EMAIL>" required>
                    <small>用于生成访问令牌的管理员账户</small>
                </div>

                <div class="form-group">
                    <label for="adminPassword">管理员密码:</label>
                    <input type="password" id="adminPassword" placeholder="123456" required>
                    <small>管理员账户密码</small>
                </div>

                <div class="button-group">
                    <button id="generateTokenBtn" class="generate-btn">
                        <i class="fas fa-magic"></i> 生成Token
                    </button>
                    
                    <button id="clearTokenBtn" class="clear-token-btn">
                        <i class="fas fa-trash"></i> 清除Token
                    </button>
                </div>

                <div class="token-result">
                    <h3>Token状态</h3>
                    <div id="tokenStatus" class="token-status">
                        <span id="statusText">检查中...</span>
                    </div>
                    
                    <div id="tokenDisplay" class="token-display" style="display: none;">
                        <label for="tokenValue">当前Token:</label>
                        <div class="token-input-group">
                            <input type="text" id="tokenValue" readonly>
                            <button id="copyTokenBtn" class="copy-btn">
                                <i class="fas fa-copy"></i> 复制
                            </button>
                        </div>
                        <small>请复制此Token到邮箱生成页面使用</small>
                    </div>
                </div>

                <div class="navigation">
                    <a href="index.html" class="nav-link">
                        <i class="fas fa-envelope"></i> 前往邮箱生成页面
                    </a>
                </div>
            </div>
        </div>
    </div>

    <footer class="footer">
        <p>&copy; 2024 Token生成器 - 基于 Cloudflare Pages 部署</p>
    </footer>

    <script src="token.js"></script>
</body>
</html>
