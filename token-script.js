// API 配置
const API_CONFIG = {
    baseUrl: 'https://eoceshi.552500.xyz',
    // 管理员账户信息
    adminEmail: '<EMAIL>',
    adminPassword: '123456'
};

// Token 存储键名
const TOKEN_PERMANENT_KEY = 'email_generator_permanent_token';
const TOKEN_TIMESTAMP_KEY = 'email_generator_token_timestamp';

// 全局变量
let isGenerating = false;

// DOM 元素
const elements = {
    generateTokenBtn: document.getElementById('generateTokenBtn'),
    clearTokenBtn: document.getElementById('clearTokenBtn'),
    adminEmail: document.getElementById('adminEmail'),
    adminPassword: document.getElementById('adminPassword'),
    statusText: document.getElementById('statusText'),
    tokenInfo: document.getElementById('tokenInfo'),
    tokenValue: document.getElementById('tokenValue'),
    copyTokenBtn: document.getElementById('copyTokenBtn')
};

// Token 管理函数
function savePermanentToken(token) {
    localStorage.setItem(TOKEN_PERMANENT_KEY, token);
    localStorage.setItem(TOKEN_TIMESTAMP_KEY, Date.now().toString());
    console.log('永久Token已保存到本地存储');
}

function loadPermanentToken() {
    const savedToken = localStorage.getItem(TOKEN_PERMANENT_KEY);
    const timestamp = localStorage.getItem(TOKEN_TIMESTAMP_KEY);
    
    if (savedToken) {
        console.log('从本地存储加载永久Token:', savedToken.substring(0, 8) + '...');
        if (timestamp) {
            const tokenAge = Date.now() - parseInt(timestamp);
            const days = Math.round(tokenAge / (24 * 60 * 60 * 1000));
            console.log('Token 年龄:', days, '天');
        }
        return savedToken;
    }

    console.log('没有找到永久Token');
    return null;
}

function clearPermanentToken() {
    localStorage.removeItem(TOKEN_PERMANENT_KEY);
    localStorage.removeItem(TOKEN_TIMESTAMP_KEY);
    console.log('永久Token已清除');
}

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    
    // 预填管理员信息
    elements.adminEmail.value = API_CONFIG.adminEmail;
    elements.adminPassword.value = API_CONFIG.adminPassword;

    // 检查已保存的Token
    checkTokenStatus();
});

// 事件监听器
function initializeEventListeners() {
    elements.generateTokenBtn.addEventListener('click', handleGenerateToken);
    elements.clearTokenBtn.addEventListener('click', handleClearToken);
    elements.copyTokenBtn.addEventListener('click', handleCopyToken);
}

// 检查Token状态
function checkTokenStatus() {
    const savedToken = loadPermanentToken();
    
    if (savedToken) {
        updateStatus('已找到保存的Token', 'success');
        showTokenInfo(savedToken);
    } else {
        updateStatus('未找到Token，请生成新的Token', 'info');
        hideTokenInfo();
    }
}

// 更新状态显示
function updateStatus(text, type = 'info') {
    elements.statusText.textContent = text;
    
    const statusDisplay = document.getElementById('tokenStatus');
    statusDisplay.className = 'status-display';
    
    if (type === 'success') {
        statusDisplay.style.borderLeftColor = '#38a169';
        statusDisplay.style.backgroundColor = '#f0fff4';
    } else if (type === 'error') {
        statusDisplay.style.borderLeftColor = '#e53e3e';
        statusDisplay.style.backgroundColor = '#fed7d7';
    } else {
        statusDisplay.style.borderLeftColor = '#667eea';
        statusDisplay.style.backgroundColor = '#f7fafc';
    }
}

// 显示Token信息
function showTokenInfo(token) {
    elements.tokenValue.value = token;
    elements.tokenInfo.style.display = 'block';
}

// 隐藏Token信息
function hideTokenInfo() {
    elements.tokenInfo.style.display = 'none';
}

// 处理生成Token按钮点击
async function handleGenerateToken() {
    if (isGenerating) return;

    const adminEmail = elements.adminEmail.value.trim();
    const adminPassword = elements.adminPassword.value.trim();

    // 验证输入
    if (!adminEmail) {
        alert('请输入管理员邮箱');
        return;
    }

    if (!adminPassword) {
        alert('请输入管理员密码');
        return;
    }

    // 开始生成
    isGenerating = true;
    elements.generateTokenBtn.disabled = true;
    elements.generateTokenBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 生成中...';
    
    try {
        updateStatus('正在生成Token...', 'info');
        
        const result = await generateToken(adminEmail, adminPassword);
        
        if (result.success) {
            savePermanentToken(result.token);
            updateStatus('Token生成成功！', 'success');
            showTokenInfo(result.token);
            alert('Token生成成功！已自动保存到本地存储。');
        } else {
            updateStatus(`Token生成失败: ${result.error}`, 'error');
            alert(`Token生成失败: ${result.error}`);
        }
    } catch (error) {
        console.error('生成Token过程出错:', error);
        updateStatus(`生成过程出现错误: ${error.message}`, 'error');
        alert(`生成过程出现错误: ${error.message}`);
    } finally {
        isGenerating = false;
        elements.generateTokenBtn.disabled = false;
        elements.generateTokenBtn.innerHTML = '<i class="fas fa-magic"></i> 生成Token';
    }
}

// 处理清除Token按钮点击
function handleClearToken() {
    if (confirm('确定要清除保存的Token吗？清除后需要重新生成Token才能使用。')) {
        clearPermanentToken();
        updateStatus('Token已清除，请重新生成', 'info');
        hideTokenInfo();
        alert('Token已清除');
    }
}

// 处理复制Token按钮点击
function handleCopyToken() {
    const token = elements.tokenValue.value;
    
    if (!token) {
        alert('没有Token可复制');
        return;
    }
    
    navigator.clipboard.writeText(token).then(() => {
        alert('Token已复制到剪贴板');
        
        // 临时改变按钮文字
        const originalText = elements.copyTokenBtn.innerHTML;
        elements.copyTokenBtn.innerHTML = '<i class="fas fa-check"></i> 已复制';
        elements.copyTokenBtn.style.background = '#38a169';
        
        setTimeout(() => {
            elements.copyTokenBtn.innerHTML = originalText;
            elements.copyTokenBtn.style.background = '';
        }, 2000);
    }).catch(err => {
        console.error('复制失败:', err);
        alert('复制失败，请手动选择文本复制');
    });
}

// 生成Token
async function generateToken(adminEmail, adminPassword) {
    console.log('开始生成Token，管理员邮箱:', adminEmail);

    try {
        const requestBody = {
            email: adminEmail,
            password: adminPassword
        };

        console.log('Token 请求参数:', requestBody);

        const response = await fetch(`${API_CONFIG.baseUrl}/api/public/genToken`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
        });

        console.log('Token 响应状态:', response.status, response.statusText);

        const data = await response.json();
        console.log('Token 响应数据:', data);

        if (response.ok && data.code === 200) {
            const token = data.data.token;
            console.log('Token生成成功:', token);
            return { success: true, token: token };
        } else {
            const errorMsg = `Token生成失败 - 状态码: ${response.status}, 错误: ${data.message || '未知错误'}`;
            console.error(errorMsg);
            return { success: false, error: errorMsg };
        }
    } catch (error) {
        const errorMsg = `生成Token请求失败: ${error.message}`;
        console.error(errorMsg, error);
        throw new Error(errorMsg);
    }
}
