<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Token管理器</title>
    <link rel="stylesheet" href="token-style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1><i class="fas fa-key"></i> Token管理器</h1>
            <p>生成和管理访问令牌</p>
        </header>

        <div class="main-content">
            <div class="token-section">
                <h2>管理员配置</h2>

                <div class="form-group">
                    <label for="adminEmail">管理员邮箱:</label>
                    <input type="email" id="adminEmail" placeholder="<EMAIL>" required>
                    <small>用于生成访问令牌的管理员账户</small>
                </div>

                <div class="form-group">
                    <label for="adminPassword">管理员密码:</label>
                    <input type="password" id="adminPassword" placeholder="123456" required>
                    <small>管理员账户密码</small>
                </div>

                <div class="button-group">
                    <button id="generateTokenBtn" class="generate-btn">
                        <i class="fas fa-magic"></i> 生成Token
                    </button>
                    <button id="clearTokenBtn" class="clear-btn">
                        <i class="fas fa-trash"></i> 清除Token
                    </button>
                </div>

                <div class="token-display">
                    <h3>当前Token状态</h3>
                    <div id="tokenStatus" class="status-display">
                        <span id="statusText">检查中...</span>
                    </div>
                    
                    <div id="tokenInfo" class="token-info" style="display: none;">
                        <label for="tokenValue">Token值:</label>
                        <div class="token-input-group">
                            <input type="text" id="tokenValue" readonly>
                            <button id="copyTokenBtn" class="copy-btn">
                                <i class="fas fa-copy"></i> 复制
                            </button>
                        </div>
                        <small>请复制此Token到邮箱生成器中使用</small>
                    </div>
                </div>

                <div class="navigation">
                    <a href="index.html" class="nav-link">
                        <i class="fas fa-envelope"></i> 前往邮箱生成器
                    </a>
                </div>
            </div>

            <div class="info-section">
                <h2>使用说明</h2>
                <div class="info-content">
                    <div class="step">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <h4>生成Token</h4>
                            <p>输入管理员邮箱和密码，点击"生成Token"按钮</p>
                        </div>
                    </div>
                    
                    <div class="step">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <h4>复制Token</h4>
                            <p>生成成功后，复制显示的Token值</p>
                        </div>
                    </div>
                    
                    <div class="step">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <h4>使用Token</h4>
                            <p>在邮箱生成器中粘贴Token值即可开始生成邮箱</p>
                        </div>
                    </div>
                </div>

                <div class="tips">
                    <h3><i class="fas fa-lightbulb"></i> 提示</h3>
                    <ul>
                        <li>Token生成后会自动保存在浏览器中</li>
                        <li>Token具有较长的有效期，无需频繁重新生成</li>
                        <li>如遇到权限问题，可尝试清除并重新生成Token</li>
                        <li>请妥善保管Token，不要泄露给他人</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <footer class="footer">
        <p>&copy; 2024 Token管理器 - 基于 Cloudflare Pages 部署</p>
    </footer>

    <script src="token-script.js"></script>
</body>
</html>
