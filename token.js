// API 配置
const API_CONFIG = {
    baseUrl: 'https://eoceshi.552500.xyz',
    // 管理员账户信息
    adminEmail: '<EMAIL>',
    adminPassword: '123456'
};

// Token 存储键名
const TOKEN_PERMANENT_KEY = 'email_generator_permanent_token';
const TOKEN_TIMESTAMP_KEY = 'email_generator_token_timestamp';

// 全局变量
let isGenerating = false;

// DOM 元素
const elements = {
    generateTokenBtn: document.getElementById('generateTokenBtn'),
    clearTokenBtn: document.getElementById('clearTokenBtn'),
    adminEmail: document.getElementById('adminEmail'),
    adminPassword: document.getElementById('adminPassword'),
    statusText: document.getElementById('statusText'),
    tokenDisplay: document.getElementById('tokenDisplay'),
    tokenValue: document.getElementById('tokenValue'),
    copyTokenBtn: document.getElementById('copyTokenBtn')
};

// Token 管理函数
function savePermanentToken(token) {
    localStorage.setItem(TOKEN_PERMANENT_KEY, token);
    localStorage.setItem(TOKEN_TIMESTAMP_KEY, Date.now().toString());
    console.log('永久Token已保存到本地存储');
}

function loadPermanentToken() {
    const savedToken = localStorage.getItem(TOKEN_PERMANENT_KEY);
    
    if (savedToken) {
        console.log('从本地存储加载永久Token:', savedToken.substring(0, 8) + '...');
        return savedToken;
    }

    console.log('没有找到永久Token');
    return null;
}

function clearPermanentToken() {
    localStorage.removeItem(TOKEN_PERMANENT_KEY);
    localStorage.removeItem(TOKEN_TIMESTAMP_KEY);
    console.log('永久Token已清除');
}

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    
    // 预填管理员信息
    elements.adminEmail.value = API_CONFIG.adminEmail;
    elements.adminPassword.value = API_CONFIG.adminPassword;

    // 检查现有token
    checkExistingToken();
});

// 事件监听器
function initializeEventListeners() {
    elements.generateTokenBtn.addEventListener('click', handleGenerateToken);
    elements.clearTokenBtn.addEventListener('click', handleClearToken);
    elements.copyTokenBtn.addEventListener('click', handleCopyToken);
}

// 检查现有token
function checkExistingToken() {
    const existingToken = loadPermanentToken();
    
    if (existingToken) {
        updateStatus('已存在永久Token', 'success');
        showToken(existingToken);
    } else {
        updateStatus('未找到Token，请生成新的Token', 'info');
        hideToken();
    }
}

// 处理生成token按钮点击
async function handleGenerateToken() {
    if (isGenerating) return;

    const adminEmail = elements.adminEmail.value.trim();
    const adminPassword = elements.adminPassword.value.trim();

    // 验证输入
    if (!adminEmail) {
        alert('请输入管理员邮箱');
        return;
    }

    if (!adminPassword) {
        alert('请输入管理员密码');
        return;
    }

    // 开始生成
    isGenerating = true;
    elements.generateTokenBtn.disabled = true;
    elements.generateTokenBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 生成中...';
    
    try {
        updateStatus('正在生成Token...', 'info');
        
        const result = await generatePermanentToken(adminEmail, adminPassword);
        
        if (result.success) {
            updateStatus('Token生成成功！', 'success');
            showToken(result.token);
            alert('Token生成成功！请复制Token到邮箱生成页面使用。');
        } else {
            updateStatus(`Token生成失败: ${result.error}`, 'error');
            hideToken();
            alert(`Token生成失败: ${result.error}`);
        }
    } catch (error) {
        console.error('生成Token过程出错:', error);
        updateStatus(`生成Token出现错误: ${error.message}`, 'error');
        hideToken();
        alert(`生成Token出现错误: ${error.message}`);
    } finally {
        isGenerating = false;
        elements.generateTokenBtn.disabled = false;
        elements.generateTokenBtn.innerHTML = '<i class="fas fa-magic"></i> 生成Token';
    }
}

// 处理清除token按钮点击
function handleClearToken() {
    if (confirm('确定要清除永久令牌吗？清除后需要重新生成令牌才能使用。')) {
        clearPermanentToken();
        updateStatus('永久Token已清除', 'info');
        hideToken();
        alert('永久Token已清除');
    }
}

// 处理复制token按钮点击
function handleCopyToken() {
    const token = elements.tokenValue.value;
    
    if (!token) {
        alert('没有Token可复制');
        return;
    }
    
    navigator.clipboard.writeText(token).then(() => {
        alert('Token已复制到剪贴板');
    }).catch(err => {
        console.error('复制失败:', err);
        // 备用复制方法
        elements.tokenValue.select();
        document.execCommand('copy');
        alert('Token已复制到剪贴板');
    });
}

// 生成永久Token
async function generatePermanentToken(adminEmail, adminPassword) {
    console.log('开始生成永久Token，管理员邮箱:', adminEmail);

    try {
        const requestBody = {
            email: adminEmail,
            password: adminPassword
        };

        console.log('Token 请求参数:', requestBody);

        const response = await fetch(`${API_CONFIG.baseUrl}/api/public/genToken`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
        });

        console.log('Token 响应状态:', response.status, response.statusText);

        const data = await response.json();
        console.log('Token 响应数据:', data);

        if (response.ok && data.code === 200) {
            const token = data.data.token;
            // 保存为永久token
            savePermanentToken(token);
            console.log('永久Token生成成功:', token);
            return { success: true, token: token };
        } else {
            const errorMsg = `Token 生成失败 - 状态码: ${response.status}, 错误: ${data.message || '未知错误'}`;
            console.error(errorMsg);
            return { success: false, error: errorMsg };
        }
    } catch (error) {
        const errorMsg = `生成 Token 请求失败: ${error.message}`;
        console.error(errorMsg, error);
        return { success: false, error: errorMsg };
    }
}

// 更新状态显示
function updateStatus(text, type = 'info') {
    elements.statusText.textContent = text;
    elements.statusText.className = `status-${type}`;
}

// 显示token
function showToken(token) {
    elements.tokenValue.value = token;
    elements.tokenDisplay.style.display = 'block';
}

// 隐藏token
function hideToken() {
    elements.tokenValue.value = '';
    elements.tokenDisplay.style.display = 'none';
}
